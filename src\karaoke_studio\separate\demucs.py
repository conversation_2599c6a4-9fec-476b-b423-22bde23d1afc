import subprocess
import sys
import os
from pathlib import Path
from typing import <PERSON><PERSON>

def separate_vocals(media_path: Path, out_root: Path) -> Tuple[Path, Path]:
    out_root.mkdir(parents=True, exist_ok=True)
    title = media_path.stem
    model_dir = out_root / "htdemucs" / title
    vocals = model_dir / "vocals.wav"
    inst = model_dir / "no_vocals.wav"
    if vocals.exists() and inst.exists():
        return vocals, inst

    # Use the virtual environment Python explicitly
    venv_python = Path(__file__).parent.parent.parent.parent / ".venv" / "Scripts" / "python.exe"
    python_exe = str(venv_python) if venv_python.exists() else sys.executable

    cmd = [
        python_exe, "-X", "utf8", "-m", "demucs",
        "--two-stems=vocals",
        "-o", str(out_root),
        str(media_path),
    ]
    try:
        # Ensure subprocess uses the virtual environment
        env = os.environ.copy()
        if venv_python.exists():
            venv_scripts = venv_python.parent
            env["PATH"] = str(venv_scripts) + os.pathsep + env.get("PATH", "")

        result = subprocess.run(cmd, check=True, capture_output=True, text=True, cwd=Path.cwd(), env=env)
    except subprocess.CalledProcessError as e:
        error_msg = (
            f"Demucs command failed with return code {e.returncode}\n"
            f"Command: {' '.join(cmd)}\n"
            f"Stdout: {e.stdout}\n"
            f"Stderr: {e.stderr}"
        )
        raise RuntimeError(error_msg) from e
    return vocals, inst
