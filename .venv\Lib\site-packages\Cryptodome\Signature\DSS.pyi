from typing import Union, Optional, Callable
from typing_extensions import Protocol

from Cryptodome.PublicKey.DSA import <PERSON><PERSON><PERSON><PERSON>
from Cryptodome.PublicKey.ECC import EccKey

class Hash(Protocol):
    def digest(self) -> bytes: ...

__all__ = ['new']

class DssSigScheme:
    def __init__(self, key: Union[DsaKey, EccKey], encoding: str, order: int) -> None: ...
    def can_sign(self) -> bool: ...
    def sign(self, msg_hash: Hash) -> bytes: ...
    def verify(self, msg_hash: Hash, signature: bytes) -> bool: ...

class DeterministicDsaSigScheme(DssSigScheme):
    def __init__(self, key, encoding, order, private_key) -> None: ...

class FipsDsaSigScheme(DssSigScheme):
    def __init__(self, key: DsaKey, encoding: str, order: int, randfunc: Callable) -> None: ...

class FipsEcDsaSigScheme(DssSigScheme):
    def __init__(self, key: <PERSON>cc<PERSON><PERSON>, encoding: str, order: int, randfunc: Callable) -> None: ...

def new(key: Union[DsaKey, EccKey], mode: str, encoding: Optional[str]='binary', randfunc: Optional[Callable]=None) -> Union[DeterministicDsaSigScheme, FipsDsaSigScheme, FipsEcDsaSigScheme]: ...
