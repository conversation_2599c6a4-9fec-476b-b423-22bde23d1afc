from __future__ import annotations
from pathlib import Path
from typing import Optional
import sys
from PyQt6 import QtWidgets, QtCore
from ..utils.config import MEDIA, STEMS
from ..utils.library import ensure_schema, list_tracks, upsert_track, delete_track
from ..download.ytdlp import download_youtube, sanitize_youtube_url
from ..separate.demucs import separate_vocals
from ..analyze.audio import estimate_bpm_and_key
from ..playback.player import Player

class MainWindow(QtWidgets.QMainWindow):
    def __init__(self):
        super().__init__()
        ensure_schema()
        self.setWindowTitle("Karaoke Studio")
        self.resize(1200, 720)
        central = QtWidgets.QWidget(self); self.setCentralWidget(central)
        layout = QtWidgets.QVBoxLayout(central)

        # Top controls
        top = QtWidgets.QHBoxLayout()
        self.url = QtWidgets.QLineEdit(self)
        self.url.setPlaceholderText("Paste a YouTube URL...")
        self.btn_add = QtWidgets.QPushButton("Add", self)
        self.btn_add.clicked.connect(self.on_add)
        top.addWidget(self.url, 1); top.addWidget(self.btn_add)
        layout.addLayout(top)

        # Table + video
        split = QtWidgets.QSplitter(self)
        split.setOrientation(QtCore.Qt.Orientation.Horizontal)
        # left: table
        left = QtWidgets.QWidget(self); lbox = QtWidgets.QVBoxLayout(left)
        self.table = QtWidgets.QTableWidget(0, 3, self)
        self.table.setHorizontalHeaderLabels(["Title","BPM","Key"])
        self.table.horizontalHeader().setStretchLastSection(True)
        self.table.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setEditTriggers(QtWidgets.QAbstractItemView.EditTrigger.NoEditTriggers)
        lbox.addWidget(self.table, 1)
        btns = QtWidgets.QHBoxLayout()
        self.btn_play = QtWidgets.QPushButton("Play Selected"); self.btn_play.clicked.connect(self.on_play)
        self.btn_stop = QtWidgets.QPushButton("Stop"); self.btn_stop.clicked.connect(self.on_stop)
        self.btn_del = QtWidgets.QPushButton("Delete"); self.btn_del.clicked.connect(self.on_delete)
        btns.addWidget(self.btn_play); btns.addWidget(self.btn_stop); btns.addWidget(self.btn_del)
        lbox.addLayout(btns)
        split.addWidget(left)

        # right: video area
        right = QtWidgets.QWidget(self); rbox = QtWidgets.QVBoxLayout(right)
        self.video = QtWidgets.QFrame(self)
        self.video.setFrameShape(QtWidgets.QFrame.Shape.Panel); self.video.setFrameShadow(QtWidgets.QFrame.Shadow.Sunken)
        self.video.setMinimumHeight(360)
        rbox.addWidget(self.video, 1)
        cbox = QtWidgets.QHBoxLayout()
        self.chk_v = QtWidgets.QCheckBox("Vocals"); self.chk_v.setChecked(True); self.chk_v.toggled.connect(self._on_mix_changed)
        self.chk_i = QtWidgets.QCheckBox("Instrumental"); self.chk_i.setChecked(True); self.chk_i.toggled.connect(self._on_mix_changed)
        cbox.addWidget(self.chk_v); cbox.addWidget(self.chk_i)
        self.pitch = QtWidgets.QSlider(QtCore.Qt.Orientation.Horizontal); self.pitch.setMinimum(-6); self.pitch.setMaximum(6)
        self.pitch.setValue(0); self.pitch.setTickInterval(1); self.pitch.valueChanged.connect(self._on_pitch)
        self.pitch_label = QtWidgets.QLabel("Pitch: 0 st")
        cbox.addWidget(self.pitch_label); cbox.addWidget(self.pitch, 1)
        rbox.addLayout(cbox)
        self.log = QtWidgets.QPlainTextEdit(self); self.log.setReadOnly(True); self.log.setMaximumBlockCount(500)
        rbox.addWidget(self.log, 1)
        split.addWidget(right)
        layout.addWidget(split, 1)

        self.player = Player(int(self.video.winId()))
        self.reload_table()

    # --- helpers
    def append(self, text: str): self.log.appendPlainText(text)

    def reload_table(self):
        rows = list(list_tracks())
        self.table.setRowCount(len(rows))
        for i, r in enumerate(rows):
            for j, val in enumerate([r["title"], f'{r["bpm"]:.1f}' if r["bpm"] else "", r["key"] or ""]):
                item = QtWidgets.QTableWidgetItem(val)
                self.table.setItem(i, j, item)

    def current_row_id(self) -> Optional[int]:
        idxs = self.table.selectionModel().selectedRows()
        if not idxs: return None
        # map row index back to DB row via latest list_tracks order
        rows = list(list_tracks())
        return rows[idxs[0].row()]["id"]

    # --- ui actions
    def on_add(self):
        url = self.url.text().strip()
        if not url: return
        self.append(f"Start: {url}")
        self.setEnabled(False)
        QtCore.QTimer.singleShot(50, lambda: self._do_add(url))

    def _do_add(self, url: str):
        url = sanitize_youtube_url(url)  # Clean Mix/playlist params
        try:
            self.append(f"Start: {url}")
            media, title, yt_id = download_youtube(url, MEDIA)
            self.append(f"Saved media: {media.name}")
            self.append("Separating stems with Demucs…")
            voc, inst = separate_vocals(media, STEMS)
            self.append(f"Stems ready: vocals={voc.name}, instrumental={inst.name}")
            self.append("Analyzing BPM / key…")
            bpm, key = estimate_bpm_and_key(inst)
            bpm_s = f"{bpm:.1f}" if bpm else "?"
            self.append(f"Analysis: BPM={bpm_s}  Key={key or '?'}")
            upsert_track(title=title, yt_id=yt_id, video_path=media,
                         vocals_path=voc, inst_path=inst, bpm=bpm, key=key)
            self.append(f"Added: {title}")
            self.reload_table()
        except Exception as e:
            self.append(f"[ERROR] {e}")
        finally:
            self.setEnabled(True)

    def on_play(self):
        tid = self.current_row_id()
        if tid is None: return
        rows = list(list_tracks())
        rec = next(r for r in rows if r["id"] == tid)
        video = Path(rec["video_path"])
        voc = Path(rec["vocals_path"]) if rec["vocals_path"] else None
        inst = Path(rec["inst_path"]) if rec["inst_path"] else None
        self.append("Playing…")
        self.player.load(video, voc, inst)
        self._on_mix_changed(); self._on_pitch()

    def on_stop(self):
        self.player.stop()
        self.append("Stopped.")

    def on_delete(self):
        tid = self.current_row_id()
        if tid is None: return
        if QtWidgets.QMessageBox.question(self, "Delete",
                "Delete the selected track and remove files from disk?") == QtWidgets.QMessageBox.StandardButton.Yes:
            ok = delete_track(tid)
            self.append("Deleted." if ok else "Delete failed.")
            self.reload_table()

    def _on_mix_changed(self):
        self.player.set_levels(self.chk_v.isChecked(), self.chk_i.isChecked())

    def _on_pitch(self):
        semi = int(self.pitch.value())
        self.pitch_label.setText(f"Pitch: {semi} st")
        self.player.set_pitch(semi)

def main():
    app = QtWidgets.QApplication(sys.argv)
    w = MainWindow(); w.show()
    sys.exit(app.exec())
